#!/usr/bin/env python3
import sys

def solve_forest(grid, start_pos, rotation_steps):
    """
    Another interpretation: Record the actual movement direction
    in the original coordinate system, regardless of rotations.
    """
    
    # Direction vectors: Up, Right, Down, Left
    directions = [(-1, 0), (0, 1), (1, 0), (0, -1)]
    dir_names = ['U', 'R', 'D', 'L']
    
    rows, cols = len(grid), len(grid[0])
    current_dir = 0  # Start facing up (^)
    pos = start_pos
    runes = []
    move_count = 0
    
    def is_valid_pos(r, c):
        return 0 <= r < rows and 0 <= c < cols
    
    def is_blocked(r, c):
        if not is_valid_pos(r, c):
            return False
        return grid[r][c] == '#'
    
    def rotate_grid_left(grid):
        rows, cols = len(grid), len(grid[0])
        new_grid = []
        for c in range(cols-1, -1, -1):
            new_row = []
            for r in range(rows):
                new_row.append(grid[r][c])
            new_grid.append(new_row)
        return new_grid
    
    def find_new_position_after_rotation(old_pos, old_cols):
        old_r, old_c = old_pos
        new_r = old_cols - 1 - old_c
        new_c = old_r
        return (new_r, new_c)
    
    while True:
        r, c = pos
        
        if not is_valid_pos(r, c):
            break
            
        dr, dc = directions[current_dir]
        next_r, next_c = r + dr, c + dc
        
        if is_blocked(next_r, next_c):
            current_dir = (current_dir + 1) % 4
        else:
            # Record the movement direction as a rune
            runes.append(dir_names[current_dir])
            pos = (next_r, next_c)
            move_count += 1
            
            # Check if forest should rotate
            if move_count % rotation_steps == 0:
                old_cols = cols
                grid = rotate_grid_left(grid)
                rows, cols = len(grid), len(grid[0])
                
                if is_valid_pos(pos[0], pos[1]):
                    pos = find_new_position_after_rotation(pos, old_cols)
                
                current_dir = (current_dir - 1) % 4
        
        if len(runes) > 10000:
            break
    
    return ''.join(runes)

def parse_input(filename):
    with open(filename, 'r') as f:
        lines = f.read().strip().split('\n')
    
    rotation_steps = int(lines[0])
    
    grid = []
    start_pos = None
    
    for i, line in enumerate(lines[1:], 1):
        row = list(line)
        grid.append(row)
        
        for j, cell in enumerate(row):
            if cell == '^':
                start_pos = (i-1, j)
                row[j] = '.'
    
    return grid, start_pos, rotation_steps

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 solution_v3.py input.txt")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        grid, start_pos, rotation_steps = parse_input(filename)
        result = solve_forest(grid, start_pos, rotation_steps)
        print(result)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
