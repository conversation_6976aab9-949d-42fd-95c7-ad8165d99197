#!/usr/bin/env python3
import sys

def solve_forest(grid, start_pos, rotation_steps):
    """
    Alternative interpretation: Maybe the runes are based on the 
    original direction system, not the current rotated system.
    """
    
    # Direction vectors: Up, Right, Down, Left
    directions = [(-1, 0), (0, 1), (1, 0), (0, -1)]
    dir_names = ['U', 'R', 'D', 'L']
    
    rows, cols = len(grid), len(grid[0])
    current_dir = 0  # Start facing up (^)
    pos = start_pos
    runes = []  # The runes we collect (output)
    move_count = 0
    
    def is_valid_pos(r, c):
        return 0 <= r < rows and 0 <= c < cols
    
    def is_blocked(r, c):
        if not is_valid_pos(r, c):
            return False
        return grid[r][c] == '#'
    
    def rotate_grid_left(grid):
        """Rotate the grid 90 degrees counter-clockwise"""
        rows, cols = len(grid), len(grid[0])
        new_grid = []
        for c in range(cols-1, -1, -1):
            new_row = []
            for r in range(rows):
                new_row.append(grid[r][c])
            new_grid.append(new_row)
        return new_grid
    
    def find_new_position_after_rotation(old_pos, old_cols):
        """Find where the position ends up after grid rotation"""
        old_r, old_c = old_pos
        new_r = old_cols - 1 - old_c
        new_c = old_r
        return (new_r, new_c)
    
    # Track how many rotations have happened to map back to original directions
    total_rotations = 0
    
    while True:
        r, c = pos
        
        # Check if we've escaped
        if not is_valid_pos(r, c):
            break
            
        # Try to move forward in current direction
        dr, dc = directions[current_dir]
        next_r, next_c = r + dr, c + dc
        
        if is_blocked(next_r, next_c):
            # Blocked - turn right
            current_dir = (current_dir + 1) % 4
        else:
            # Move forward
            pos = (next_r, next_c)
            move_count += 1
            
            # The rune is based on the original direction system
            # Map current direction back to original system
            original_dir = (current_dir + total_rotations) % 4
            runes.append(dir_names[original_dir])
            
            # Check if forest should rotate
            if move_count % rotation_steps == 0:
                old_cols = cols
                grid = rotate_grid_left(grid)
                rows, cols = len(grid), len(grid[0])
                
                # Update position after rotation
                if is_valid_pos(pos[0], pos[1]):
                    pos = find_new_position_after_rotation(pos, old_cols)
                
                # Update direction after rotation
                current_dir = (current_dir - 1) % 4
                total_rotations = (total_rotations + 1) % 4
        
        # Safety check
        if len(runes) > 10000:
            break
    
    return ''.join(runes)

def parse_input(filename):
    """Parse the input file to extract grid and parameters"""
    with open(filename, 'r') as f:
        lines = f.read().strip().split('\n')
    
    rotation_steps = int(lines[0])
    
    grid = []
    start_pos = None
    
    for i, line in enumerate(lines[1:], 1):
        row = list(line)
        grid.append(row)
        
        # Find starting position (^)
        for j, cell in enumerate(row):
            if cell == '^':
                start_pos = (i-1, j)
                row[j] = '.'  # Replace with empty space
    
    return grid, start_pos, rotation_steps

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 solution_v2.py input.txt")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        grid, start_pos, rotation_steps = parse_input(filename)
        result = solve_forest(grid, start_pos, rotation_steps)
        print(result)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
