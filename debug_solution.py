#!/usr/bin/env python3
import sys

def solve_forest_debug(grid, start_pos, rotation_steps):
    """Debug version with detailed logging"""
    
    # Direction vectors: Up, Right, Down, Left
    directions = [(-1, 0), (0, 1), (1, 0), (0, -1)]
    dir_names = ['U', 'R', 'D', 'L']
    
    rows, cols = len(grid), len(grid[0])
    current_dir = 0  # Start facing up (^)
    pos = start_pos
    path = []
    move_count = 0
    
    def print_grid(grid, pos, direction, step):
        print(f"Step {step}: Position {pos}, Direction {dir_names[direction]}, Moves: {move_count}")
        for i, row in enumerate(grid):
            line = ""
            for j, cell in enumerate(row):
                if (i, j) == pos:
                    line += dir_names[direction].lower()
                else:
                    line += cell
            print(line)
        print()
    
    def is_valid_pos(r, c):
        return 0 <= r < rows and 0 <= c < cols
    
    def is_blocked(r, c):
        if not is_valid_pos(r, c):
            return False
        return grid[r][c] == '#'
    
    def rotate_grid_left(grid):
        rows, cols = len(grid), len(grid[0])
        new_grid = []
        for c in range(cols-1, -1, -1):
            new_row = []
            for r in range(rows):
                new_row.append(grid[r][c])
            new_grid.append(new_row)
        return new_grid
    
    def find_new_position_after_rotation(old_pos, old_cols):
        old_r, old_c = old_pos
        new_r = old_cols - 1 - old_c
        new_c = old_r
        return (new_r, new_c)
    
    step = 0
    print_grid(grid, pos, current_dir, step)
    
    while step < 50:  # Limit for debugging
        r, c = pos
        
        if not is_valid_pos(r, c):
            print(f"Escaped at step {step}!")
            break
            
        dr, dc = directions[current_dir]
        next_r, next_c = r + dr, c + dc
        
        print(f"Trying to move from {pos} to ({next_r}, {next_c})")
        
        if is_blocked(next_r, next_c):
            print(f"Blocked! Turning right from {dir_names[current_dir]}")
            current_dir = (current_dir + 1) % 4
            print(f"Now facing {dir_names[current_dir]}")
        else:
            print(f"Moving {dir_names[current_dir]}")
            pos = (next_r, next_c)
            path.append(dir_names[current_dir])
            move_count += 1
            
            if move_count % rotation_steps == 0:
                print(f"ROTATION TIME! After {move_count} moves")
                old_cols = cols
                grid = rotate_grid_left(grid)
                rows, cols = len(grid), len(grid[0])
                
                if is_valid_pos(pos[0], pos[1]):
                    old_pos = pos
                    pos = find_new_position_after_rotation(pos, old_cols)
                    print(f"Position rotated from {old_pos} to {pos}")
                
                old_dir = current_dir
                current_dir = (current_dir - 1) % 4
                print(f"Direction rotated from {dir_names[old_dir]} to {dir_names[current_dir]}")
        
        step += 1
        print_grid(grid, pos, current_dir, step)
        print(f"Path so far: {''.join(path)}")
        print("-" * 40)
    
    return ''.join(path)

def parse_input(filename):
    with open(filename, 'r') as f:
        lines = f.read().strip().split('\n')
    
    rotation_steps = int(lines[0])
    
    grid = []
    start_pos = None
    
    for i, line in enumerate(lines[1:], 1):
        row = list(line)
        grid.append(row)
        
        for j, cell in enumerate(row):
            if cell == '^':
                start_pos = (i-1, j)
                row[j] = '.'
    
    return grid, start_pos, rotation_steps

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 debug_solution.py input.txt")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        grid, start_pos, rotation_steps = parse_input(filename)
        print(f"Rotation steps: {rotation_steps}")
        print(f"Start position: {start_pos}")
        result = solve_forest_debug(grid, start_pos, rotation_steps)
        print(f"Final result: {result}")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
